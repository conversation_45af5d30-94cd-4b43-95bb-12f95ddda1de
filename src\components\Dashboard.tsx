import React, { useState, useEffect, useMemo } from 'react';
import { Map } from './Map';
import { MessageList } from './MessageList';
import { GPSMessage, Voyage, User } from '../types';
import { WebSocketClient } from '../utils/websocket';
import { apiClient } from '../utils/api';
import '../styles/dashboard.css';

interface DashboardProps {
  user: User;
  token: string;
  onLogout: () => void;
  onError: (error: string) => void;
}

export const Dashboard: React.FC<DashboardProps> = ({
  user,
  token,
  onLogout,
  onError
}) => {
  const [messages, setMessages] = useState<GPSMessage[]>([]);
  const [voyages, setVoyages] = useState<Voyage[]>([]);
  const [selectedMessage, setSelectedMessage] = useState<GPSMessage | null>(null);
  const [selectedVoyage, setSelectedVoyage] = useState<string | null>(null);
  const [selectedImei, setSelectedImei] = useState<string>(user.imeiCodes[0] || '');
  const [wsClient, setWsClient] = useState<WebSocketClient | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  const [trackerStatus, setTrackerStatus] = useState<{ [imei: string]: boolean }>({});
  const [showImeiModal, setShowImeiModal] = useState(false);
  const [newImeiCodes, setNewImeiCodes] = useState(user.imeiCodes.join(', '));

  // Get filtered messages for display count (filtered by selected IMEI)
  const filteredAndSortedMessages = useMemo(() => {
    let baseMessages = messages;

    // Filter by selected IMEI
    if (selectedImei) {
      baseMessages = baseMessages.filter(msg => msg.imei === selectedImei);
    }

    // Filter by selected voyage
    if (selectedVoyage) {
      const voyage = voyages.find(v => v.id === selectedVoyage);
      return voyage ? voyage.messages.filter(msg => msg.imei === selectedImei) : [];
    }

    return baseMessages;
  }, [messages, selectedVoyage, voyages, selectedImei]);

  useEffect(() => {
    const initWebSocket = async () => {
      try {
        const wsUrl = `ws://${window.location.hostname}:8090/ws`;
        const client = new WebSocketClient(wsUrl);
        
        // Set up message handlers
        client.onMessage('auth', (data) => {
          if (data.success) {
            setConnectionStatus('connected');
            // Request initial data
            client.requestVoyages();
            client.requestMessages();
            client.requestTrackerStatus();
          }
        });

        client.onMessage('gps_data', (message: GPSMessage) => {
          // Convert date strings back to Date objects
          const processedMessage = {
            ...message,
            trackerTimestamp: new Date(message.trackerTimestamp),
            serverTimestamp: new Date(message.serverTimestamp)
          };
          
          setMessages(prev => [processedMessage, ...prev]);
        });

        client.onMessage('voyage_list', (voyageList: Voyage[]) => {
          const processedVoyages = voyageList.map(voyage => ({
            ...voyage,
            startTime: new Date(voyage.startTime),
            endTime: voyage.endTime ? new Date(voyage.endTime) : undefined,
            messages: voyage.messages.map(msg => ({
              ...msg,
              trackerTimestamp: new Date(msg.trackerTimestamp),
              serverTimestamp: new Date(msg.serverTimestamp)
            }))
          }));
          setVoyages(processedVoyages);
        });

        client.onMessage('message_list', (messageList: GPSMessage[]) => {
          const processedMessages = messageList.map(msg => ({
            ...msg,
            trackerTimestamp: new Date(msg.trackerTimestamp),
            serverTimestamp: new Date(msg.serverTimestamp)
          }));
          setMessages(processedMessages);
        });

        client.onMessage('tracker_status', (status: { [imei: string]: boolean }) => {
          setTrackerStatus(status);
        });

        client.onError((error) => {
          onError(`WebSocket error: ${error}`);
          setConnectionStatus('disconnected');
        });

        // Connect and authenticate
        await client.connect();
        client.authenticate(token);
        setWsClient(client);

        // Set up periodic tracker status updates
        const statusInterval = setInterval(() => {
          if (client.isConnected()) {
            client.requestTrackerStatus();
          }
        }, 30000); // Update every 30 seconds

        return () => clearInterval(statusInterval);

      } catch (error) {
        onError('Failed to connect to server');
        setConnectionStatus('disconnected');
      }
    };

    initWebSocket();

    return () => {
      if (wsClient) {
        wsClient.disconnect();
      }
    };
  }, [token, onError]);

  const handleVoyageSelect = (voyageId: string | null) => {
    setSelectedVoyage(voyageId);
    setSelectedMessage(null);
    
    if (wsClient) {
      if (voyageId) {
        wsClient.requestMessages(voyageId);
      } else {
        wsClient.requestMessages();
      }
    }
  };

  const handleMessageSelect = (message: GPSMessage) => {
    setSelectedMessage(message);
  };

  const handleUpdateImeiCodes = async () => {
    try {
      const imeiArray = newImeiCodes
        .split(',')
        .map(code => code.trim())
        .filter(code => code.length > 0);

      await apiClient.updateImeiCodes({
        token,
        imeiCodes: imeiArray
      });

      // Update local user state
      user.imeiCodes = imeiArray;
      setShowImeiModal(false);
      
      // Reconnect WebSocket to get updated data
      if (wsClient) {
        wsClient.disconnect();
        setTimeout(() => {
          window.location.reload(); // Simple way to reinitialize with new IMEI codes
        }, 1000);
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to update IMEI codes');
    }
  };

  const getTrackerStatusColor = () => {
    if (!selectedImei) return 'text-secondary';
    const isConnected = trackerStatus[selectedImei];
    return isConnected ? 'text-success' : 'text-error';
  };

  const getTrackerStatusText = () => {
    if (!selectedImei) return 'Nessun tracker';
    const isConnected = trackerStatus[selectedImei];
    return isConnected ? 'Tracker Connesso' : 'Tracker Disconnesso';
  };

  return (
    <div className="dashboard-container">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <div className="app-logo">
              <div className="logo-icon">🌍</div>
              <h1 className="app-title">ECOTrac</h1>
            </div>
            <div className="header-info">
              <span className="user-info">Welcome, {user.username}</span>
              <span className={`connection-status ${getTrackerStatusColor()}`}>
                ● {getTrackerStatusText()}
              </span>
            </div>
          </div>
          <div className="header-right">
            <button
              onClick={() => setShowImeiModal(true)}
              className="header-btn"
              title="Manage IMEI Codes"
            >
              ⚙️ Settings
            </button>
            <button
              onClick={onLogout}
              className="header-btn logout-btn"
              title="Logout"
            >
              🚪 Exit
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="dashboard-main">
        {/* Map Panel - Top 60-70% */}
        <div className="map-panel">
          <div className="panel-header">
            <div className="panel-title">
              📍 Mappa Posizioni
            </div>
            <div className="panel-controls">
              <select
                value={selectedImei}
                onChange={(e) => setSelectedImei(e.target.value)}
                className="imei-selector"
              >
                {user.imeiCodes.map(imei => (
                  <option key={imei} value={imei}>
                    {imei}
                  </option>
                ))}
              </select>
              <select
                value={selectedVoyage || 'all'}
                onChange={(e) => handleVoyageSelect(e.target.value === 'all' ? null : e.target.value)}
                className="voyage-selector"
              >
                <option value="all">Tutti i viaggi</option>
                {voyages.filter(v => v.imei === selectedImei).map(voyage => (
                  <option key={voyage.id} value={voyage.id}>
                    {new Date(voyage.startTime).toLocaleDateString()} - {new Date(voyage.startTime).toLocaleTimeString()}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="map-container">
            <Map
              messages={filteredAndSortedMessages}
              selectedMessage={selectedMessage}
              selectedVoyage={selectedVoyage}
              selectedImei={selectedImei}
              voyages={voyages}
              onMessageSelect={handleMessageSelect}
            />
          </div>
        </div>

        {/* Message List Panel - Bottom 30-40% */}
        <div className="message-panel">
          <div className="panel-header">
            <div className="panel-title">
              📊 Dettagli
            </div>
            <div className="message-count">
              {filteredAndSortedMessages.length} messaggi
            </div>
          </div>
          <div className="message-container">
            <MessageList
              messages={filteredAndSortedMessages}
              selectedMessage={selectedMessage}
              selectedVoyage={selectedVoyage}
              selectedImei={selectedImei}
              voyages={voyages}
              onMessageSelect={handleMessageSelect}
            />
          </div>
        </div>
      </div>

      {/* IMEI Management Modal */}
      {showImeiModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="card w-full max-w-md">
            <div className="card-header">
              <h3 className="card-title">Manage IMEI Codes</h3>
            </div>
            <div className="card-body">
              <div className="mb-4">
                <label className="block text-sm font-semibold mb-2">
                  IMEI Codes (comma-separated)
                </label>
                <textarea
                  value={newImeiCodes}
                  onChange={(e) => setNewImeiCodes(e.target.value)}
                  className="input h-24 resize-none"
                  placeholder="123456789012345, 987654321098765"
                />
                <p className="text-sm text-secondary mt-1">
                  Enter the IMEI codes of GPS trackers you want to monitor
                </p>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={handleUpdateImeiCodes}
                  className="btn btn-primary flex-1"
                >
                  Update
                </button>
                <button
                  onClick={() => setShowImeiModal(false)}
                  className="btn btn-secondary flex-1"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
