import WebSocket from 'ws';

console.log('🚀 Avvio test GPS simulator...');

const ws = new WebSocket('ws://localhost:8090/ws');

ws.on('open', () => {
  console.log('✅ Connesso al server GPS');
  
  // Invia un messaggio di test con l'IMEI di Stefano
  const testMessage = {
    type: 'gps_data',
    data: {
      imei: '1234567890',
      timestamp: new Date().toISOString(),
      lat: 40.7128,
      lng: -74.0060,
      speed: 45,
      status: 'GPS Fixed',
      battery: 85
    }
  };
  
  console.log('📡 Invio messaggio GPS di test:', testMessage);
  ws.send(JSON.stringify(testMessage));
  
  // Invia un secondo messaggio dopo 2 secondi
  setTimeout(() => {
    const testMessage2 = {
      type: 'gps_data',
      data: {
        imei: '1234567890',
        timestamp: new Date().toISOString(),
        lat: 40.7589,
        lng: -73.9851,
        speed: 35,
        status: 'GPS Fixed',
        battery: 83
      }
    };
    
    console.log('📡 Invio secondo messaggio GPS:', testMessage2);
    ws.send(JSON.stringify(testMessage2));
  }, 2000);
  
  // Chiudi dopo 5 secondi
  setTimeout(() => {
    console.log('🔚 Chiusura connessione test');
    ws.close();
    process.exit(0);
  }, 5000);
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log('📥 Ricevuto dal server:', message);
  } catch (error) {
    console.log('📥 Messaggio non-JSON ricevuto:', data.toString());
  }
});

ws.on('close', () => {
  console.log('❌ Disconnesso dal server');
});

ws.on('error', (error) => {
  console.error('🚨 Errore WebSocket:', error.message);
  process.exit(1);
});
