{"name": "gps-tracker-management", "version": "1.0.0", "description": "TypeScript GPS Tracker Management Application", "type": "module", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "client:dev": "vite", "server:dev": "tsx watch server/server.ts", "build": "tsc && vite build", "preview": "vite preview", "server:build": "tsc --project tsconfig.server.json", "server:start": "node dist/server/server.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-leaflet": "^4.2.1", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/leaflet": "^1.9.8", "@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "tsx": "^4.6.2", "typescript": "^5.2.2", "vite": "^5.0.8"}}