{"version": 3, "sources": ["../../react/cjs/react-jsx-dev-runtime.production.min.js", "../../react/jsx-dev-runtime.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var a=Symbol.for(\"react.fragment\");exports.Fragment=a;exports.jsxDEV=void 0;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AASa,QAAI,IAAE,OAAO,IAAI,gBAAgB;AAAE,YAAQ,WAAS;AAAE,YAAQ,SAAO;AAAA;AAAA;;;ACTlF;AAAA;AAEA,QAAI,MAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": []}