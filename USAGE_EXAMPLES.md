# GPS Tracker Management - Usage Examples

This document provides practical examples of how to use the GPS Tracker Management Application.

## Quick Start Guide

### 1. Starting the Application

```bash
# Install dependencies
npm install

# Start both frontend and backend in development mode
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8090

### 2. Creating Your First Account

1. Open http://localhost:3000 in your browser
2. Click "Register" 
3. Fill in the registration form:
   - **Username**: `testuser`
   - **Password**: `password123`
   - **IMEI Codes**: `***************, ***************`
4. Click "Register"

You'll be automatically logged in after successful registration.

### 3. Testing with Simulated GPS Data

Use the included GPS simulator to generate test data:

```bash
# In a new terminal, run the GPS simulator
node test-gps-simulator.js

# Commands in the simulator:
# - Type "start" to begin route simulation
# - Type "random" to send random GPS data
# - Type "stop" to stop simulation
# - Type "quit" to exit
```

## GPS Tracker Integration Examples

### WebSocket Connection (JavaScript)

```javascript
const WebSocket = require('ws');

// Connect to the GPS tracker server
const ws = new WebSocket('ws://localhost:8090/ws');

ws.on('open', () => {
    console.log('Connected to GPS server');
    
    // Send GPS data
    const gpsData = {
        type: 'gps_data',
        data: {
            imei: '***************',
            timestamp: new Date().toISOString(),
            lat: 40.7128,
            lng: -74.0060,
            speed: 65.5,
            status: 'Fixed',
            battery: 85
        }
    };
    
    ws.send(JSON.stringify(gpsData));
});
```

### HTTP API Examples

#### User Registration
```bash
curl -X POST http://localhost:8090/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "securepassword",
    "imeiCodes": ["***************", "***************"]
  }'
```

#### User Login
```bash
curl -X POST http://localhost:8090/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "securepassword"
  }'
```

#### Update IMEI Codes
```bash
curl -X POST http://localhost:8090/api/update-imei \
  -H "Content-Type: application/json" \
  -d '{
    "token": "your-jwt-token-here",
    "imeiCodes": ["***************", "***************", "555666777888999"]
  }'
```

## GPS Message Format Examples

### Basic Position Update
```json
{
  "type": "gps_data",
  "data": {
    "imei": "***************",
    "timestamp": "2024-01-15T10:30:00Z",
    "lat": 40.7128,
    "lng": -74.0060,
    "speed": 45.5,
    "status": "Fixed",
    "battery": 85
  }
}
```

### Trip Start
```json
{
  "type": "gps_data",
  "data": {
    "imei": "***************",
    "timestamp": "2024-01-15T09:00:00Z",
    "lat": 40.7128,
    "lng": -74.0060,
    "speed": 0,
    "status": "Trip Start",
    "battery": 90
  }
}
```

### Trip End
```json
{
  "type": "gps_data",
  "data": {
    "imei": "***************",
    "timestamp": "2024-01-15T17:30:00Z",
    "lat": 40.7589,
    "lng": -73.9851,
    "speed": 0,
    "status": "Trip End",
    "battery": 75
  }
}
```

### No GPS Fix
```json
{
  "type": "gps_data",
  "data": {
    "imei": "***************",
    "timestamp": "2024-01-15T12:15:00Z",
    "lat": 0,
    "lng": 0,
    "speed": 0,
    "status": "No Fixed",
    "battery": 80
  }
}
```

## Frontend Usage Examples

### Dashboard Features

1. **Map Interaction**
   - Click on markers to see detailed GPS information
   - Zoom and pan to explore different areas
   - View voyage tracks when a specific voyage is selected

2. **Voyage Management**
   - Select "Show All Messages" to view all tracker data
   - Click on individual voyages to see specific trip tracks
   - View voyage duration and message count

3. **Message Filtering**
   - Use status dropdown to filter by position status
   - Use IMEI dropdown to filter by specific tracker
   - Click column headers to sort data

4. **IMEI Management**
   - Click "Manage IMEI Codes" to update authorized trackers
   - Add new IMEI codes separated by commas
   - Remove access to specific trackers

## Satellite Message Handler Example

```javascript
const SatelliteMessageHandler = require('./server/satellite-handler.js');

// Initialize satellite handler
const satelliteHandler = new SatelliteMessageHandler();
satelliteHandler.initialize();

// Set GPS handler reference
satelliteHandler.setGPSHandler(gpsHandlerInstance);

// Process Iridium satellite message
const iridiumMessage = {
  protocol: 'iridium',
  data: {
    imei: '***************',
    timestamp: Date.now(),
    latitude: 40.7128,
    longitude: -74.0060,
    speed: 25.5,
    status: 'Fixed',
    battery: 85
  }
};

satelliteHandler.queueMessage(iridiumMessage);

// Get processing statistics
console.log(satelliteHandler.getStats());
```

## Production Deployment

### Environment Variables
```bash
# .env file
PORT=8090
JWT_SECRET=your-super-secure-secret-key-here
NODE_ENV=production
```

### Build and Start
```bash
# Build the application
npm run build

# Start the production server
npm run server:start
```

### Docker Example
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 8090
CMD ["npm", "run", "server:start"]
```

## Troubleshooting Examples

### Check Server Status
```bash
curl http://localhost:8090/api/health
```

### Test WebSocket Connection
```javascript
const ws = new WebSocket('ws://localhost:8090/ws');
ws.on('open', () => console.log('WebSocket connected'));
ws.on('error', (error) => console.error('WebSocket error:', error));
```

### View Server Logs
```bash
# Development mode shows logs in console
npm run dev

# Production mode
npm run server:start
```

### Common Issues

1. **Port 8090 already in use**
   ```bash
   # Find process using port 8090
   netstat -ano | findstr :8090
   
   # Kill the process (Windows)
   taskkill /PID <process-id> /F
   ```

2. **WebSocket connection failed**
   - Check if backend server is running
   - Verify firewall settings
   - Ensure correct WebSocket URL

3. **Map not loading**
   - Check internet connection for map tiles
   - Verify Leaflet CSS is loaded
   - Check browser console for errors

## Performance Tips

1. **Large Datasets**
   - Use voyage filtering to reduce displayed messages
   - Implement pagination for message lists
   - Consider database storage for production use

2. **Real-time Updates**
   - WebSocket automatically handles real-time GPS updates
   - Map markers update automatically when new data arrives
   - Message list refreshes with new incoming data

3. **Memory Management**
   - Current implementation stores data in memory
   - For production, consider implementing data retention policies
   - Monitor memory usage with large numbers of trackers

This completes the usage examples for the GPS Tracker Management Application.
