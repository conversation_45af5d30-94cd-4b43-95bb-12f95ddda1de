// GPS Message data structure (using simple objects instead of interfaces as requested)
export type GPSMessage = {
  id: string;
  imei: string;
  trackerTimestamp: Date;
  serverTimestamp: Date;
  latitude: number;
  longitude: number;
  speed: number; // km/h
  status: 'Inizio' | 'Fine' | 'GPS Fixed' | 'No GPS Fixed';
  batteryLevel: number; // percentage
  voyageId?: string;
};

export type Voyage = {
  id: string;
  imei: string;
  startTime: Date;
  endTime?: Date;
  messages: GPSMessage[];
};

export type User = {
  username: string;
  password: string;
  imeiCodes: string[];
};

export type AuthToken = {
  username: string;
  imeiCodes: string[];
  exp: number;
};

export type WebSocketMessage = {
  type: 'auth' | 'gps_data' | 'voyage_list' | 'message_list' | 'tracker_status' | 'error';
  data?: any;
  error?: string;
};
