import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:8090/api';

async function testLogin(username, password) {
  try {
    const response = await fetch(`${API_BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Login successful for ${username}:${password}`);
      console.log('Token:', data.token);
      console.log('IMEI Codes:', data.imeiCodes);
      return true;
    } else {
      const error = await response.json();
      console.log(`❌ Login failed for ${username}:${password} - ${error.error}`);
      return false;
    }
  } catch (error) {
    console.error(`🚨 Error testing ${username}:${password}`, error.message);
    return false;
  }
}

async function testCommonPasswords() {
  const username = '<PERSON>';
  const commonPasswords = [
    'password',
    'password123',
    'stefano',
    'Stefano',
    'stefano123',
    'Stefano123',
    '123456',
    'admin',
    'test',
    'test123',
    'gps123',
    'tracker',
    'ecotrac',
    'ECOTrac',
    'demo',
    'demo123'
  ];

  console.log(`🔍 Testing common passwords for user: ${username}`);
  
  for (const password of commonPasswords) {
    const success = await testLogin(username, password);
    if (success) {
      return password;
    }
    // Small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('❌ No common password found');
  return null;
}

async function registerTestUser() {
  try {
    const response = await fetch(`${API_BASE_URL}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser',
        password: 'password123',
        imeiCodes: ['1234567890', '9876543210']
      }),
    });

    if (response.ok) {
      console.log('✅ Test user registered successfully');
      return await testLogin('testuser', 'password123');
    } else {
      const error = await response.json();
      console.log('❌ Registration failed:', error.error);
      return false;
    }
  } catch (error) {
    console.error('🚨 Registration error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing GPS Tracker Management Login');
  
  // First try common passwords for Stefano
  const stefanoPassword = await testCommonPasswords();
  
  if (!stefanoPassword) {
    console.log('\n📝 Trying to register a test user...');
    await registerTestUser();
  }
}

main().catch(console.error);
