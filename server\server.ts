import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { AuthService } from './auth.js';
import { GPSHandler } from './gps-handler.js';
import { WebSocketManager } from './websocket.js';
import path from 'path';

const app = express();
const server = createServer(app);
const PORT = process.env.PORT || 8090;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize services
const authService = new AuthService();
const gpsHandler = new GPSHandler();
const wsManager = new WebSocketManager(server, authService, gpsHandler);

// REST API endpoints
app.post('/api/register', async (req, res) => {
  try {
    const { username, password, imeiCodes } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    const success = await authService.register(username, password, imeiCodes || []);
    
    if (success) {
      res.json({ success: true });
    } else {
      res.status(400).json({ error: 'Username already exists' });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    const token = await authService.login(username, password);
    
    if (token) {
      const imeiCodes = authService.getUserImeiCodes(username);
      res.json({ token, imeiCodes });
    } else {
      res.status(401).json({ error: 'Invalid credentials' });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/update-imei', async (req, res) => {
  try {
    const { token, imeiCodes } = req.body;
    
    if (!token || !Array.isArray(imeiCodes)) {
      return res.status(400).json({ error: 'Token and IMEI codes required' });
    }

    const authResult = authService.verifyToken(token);
    if (!authResult) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    const success = await authService.updateImeiCodes(authResult.username, imeiCodes);
    
    if (success) {
      res.json({ success: true });
    } else {
      res.status(400).json({ error: 'Failed to update IMEI codes' });
    }
  } catch (error) {
    console.error('Update IMEI error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check endpoint
app.get('/api/health', (_, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static('dist'));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../dist/index.html'));
  });
}

// Start server
server.listen(PORT, () => {
  console.log(`🚀 GPS Tracker Management Server running on port ${PORT}`);
  console.log(`📡 WebSocket server ready for GPS tracker connections`);
  console.log(`🌐 Web interface available at http://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

export { authService, gpsHandler, wsManager };
