// Frontend types (matching server types)
export interface GPSMessage {
  id: string;
  imei: string;
  trackerTimestamp: Date;
  serverTimestamp: Date;
  latitude: number;
  longitude: number;
  speed: number; // km/h
  status: 'Inizio' | 'Fine' | 'GPS Fixed' | 'No GPS Fixed';
  batteryLevel: number; // percentage
  voyageId?: string;
}

export interface Voyage {
  id: string;
  imei: string;
  startTime: Date;
  endTime?: Date;
  messages: GPSMessage[];
}

export interface User {
  username: string;
  imeiCodes: string[];
}

export interface WebSocketMessage {
  type: 'auth' | 'gps_data' | 'voyage_list' | 'message_list' | 'error';
  data?: any;
  error?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  user: User | null;
}

export interface AppState {
  auth: AuthState;
  messages: GPSMessage[];
  voyages: Voyage[];
  selectedVoyage: string | null;
  selectedMessage: GPSMessage | null;
  loading: boolean;
  error: string | null;
}
