import { WebSocket, WebSocketServer } from 'ws';
import { IncomingMessage } from 'http';
import { AuthService } from './auth.js';
import { GPSHandler } from './gps-handler.js';
import { WebSocketMessage } from './types.js';

export class WebSocketManager {
  private wss: WebSocketServer;
  private authService: AuthService;
  private gpsHandler: GPSHandler;
  private clients = new Map<WebSocket, { username?: string; imeiCodes?: string[] }>();

  constructor(server: any, authService: AuthService, gpsHandler: GPSHandler) {
    this.authService = authService;
    this.gpsHandler = gpsHandler;
    
    this.wss = new WebSocketServer({ 
      server,
      path: '/ws'
    });

    this.wss.on('connection', this.handleConnection.bind(this));
  }

  private handleConnection(ws: WebSocket, request: IncomingMessage): void {
    console.log('New WebSocket connection');
    
    // Initialize client data
    this.clients.set(ws, {});

    ws.on('message', (data: Buffer) => {
      try {
        const message: WebSocketMessage = JSON.parse(data.toString());
        this.handleMessage(ws, message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
        this.sendError(ws, 'Invalid message format');
      }
    });

    ws.on('close', () => {
      console.log('WebSocket connection closed');
      this.clients.delete(ws);
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.clients.delete(ws);
    });
  }

  private async handleMessage(ws: WebSocket, message: WebSocketMessage): Promise<void> {
    const clientData = this.clients.get(ws);
    if (!clientData) return;

    console.log(`📨 Received message type: ${message.type}`);

    switch (message.type) {
      case 'auth':
        await this.handleAuth(ws, message.data);
        break;

      case 'gps_data':
        // This handles GPS data from trackers (not from web clients)
        console.log(`🛰️ Processing GPS data for IMEI: ${message.data?.imei}`);
        this.handleGPSData(message.data);
        break;
        
      case 'voyage_list':
        this.handleVoyageListRequest(ws);
        break;
        
      case 'message_list':
        this.handleMessageListRequest(ws, message.data);
        break;

      case 'tracker_status':
        this.handleTrackerStatusRequest(ws);
        break;

      default:
        this.sendError(ws, 'Unknown message type');
    }
  }

  private async handleAuth(ws: WebSocket, authData: any): Promise<void> {
    try {
      const { username, password, token } = authData;
      
      let authResult = null;
      
      if (token) {
        // Verify existing token
        authResult = this.authService.verifyToken(token);
      } else if (username && password) {
        // Login with credentials
        const newToken = await this.authService.login(username, password);
        if (newToken) {
          authResult = this.authService.verifyToken(newToken);
          // Send the new token back to client
          this.send(ws, {
            type: 'auth',
            data: { token: newToken, success: true }
          });
        }
      }
      
      if (authResult) {
        const clientData = this.clients.get(ws);
        if (clientData) {
          clientData.username = authResult.username;
          clientData.imeiCodes = authResult.imeiCodes;
        }
        
        if (!token) {
          // Only send success if we're not just verifying a token
          this.send(ws, {
            type: 'auth',
            data: { success: true, imeiCodes: authResult.imeiCodes }
          });
        }
      } else {
        this.sendError(ws, 'Authentication failed');
      }
    } catch (error) {
      console.error('Auth error:', error);
      this.sendError(ws, 'Authentication error');
    }
  }

  private handleGPSData(gpsData: any): void {
    console.log(`🔄 Processing GPS data:`, gpsData);
    const message = this.gpsHandler.processGPSData(gpsData);
    if (message) {
      console.log(`✅ GPS message processed for IMEI ${message.imei}, broadcasting to clients`);
      // Broadcast to all authenticated clients who have access to this IMEI
      this.broadcastToAuthorizedClients(message.imei, {
        type: 'gps_data',
        data: message
      });
    } else {
      console.log(`❌ Failed to process GPS data`);
    }
  }

  private handleVoyageListRequest(ws: WebSocket): void {
    const clientData = this.clients.get(ws);
    if (!clientData?.imeiCodes) {
      this.sendError(ws, 'Not authenticated');
      return;
    }

    const voyages = this.gpsHandler.getVoyagesForImei(clientData.imeiCodes);
    this.send(ws, {
      type: 'voyage_list',
      data: voyages
    });
  }

  private handleMessageListRequest(ws: WebSocket, requestData: any): void {
    const clientData = this.clients.get(ws);
    if (!clientData?.imeiCodes) {
      this.sendError(ws, 'Not authenticated');
      return;
    }

    let messages;
    if (requestData?.voyageId) {
      messages = this.gpsHandler.getVoyageMessages(requestData.voyageId);
    } else {
      messages = this.gpsHandler.getMessagesForImei(clientData.imeiCodes);
    }

    this.send(ws, {
      type: 'message_list',
      data: messages
    });
  }

  private handleTrackerStatusRequest(ws: WebSocket): void {
    const clientData = this.clients.get(ws);
    if (!clientData?.imeiCodes) {
      this.sendError(ws, 'Not authenticated');
      return;
    }

    const trackerStatus = this.gpsHandler.getTrackerConnectionStatus(clientData.imeiCodes);
    this.send(ws, {
      type: 'tracker_status',
      data: trackerStatus
    });
  }

  private broadcastToAuthorizedClients(imei: string, message: WebSocketMessage): void {
    for (const [ws, clientData] of this.clients.entries()) {
      if (clientData.imeiCodes?.includes(imei)) {
        this.send(ws, message);
      }
    }
  }

  private send(ws: WebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private sendError(ws: WebSocket, error: string): void {
    this.send(ws, { type: 'error', error });
  }

  // Method to handle GPS tracker connections (separate from web client connections)
  handleGPSTrackerConnection(ws: WebSocket): void {
    ws.on('message', (data: Buffer) => {
      try {
        // Parse GPS tracker data (format depends on tracker protocol)
        const gpsData = this.parseGPSTrackerData(data);
        if (gpsData) {
          this.handleGPSData(gpsData);
        }
      } catch (error) {
        console.error('Error processing GPS tracker data:', error);
      }
    });
  }

  private parseGPSTrackerData(data: Buffer): any {
    // This is a placeholder for GPS tracker data parsing
    // The actual implementation will depend on the specific GPS tracker protocol
    // For now, we'll assume JSON format for testing
    try {
      return JSON.parse(data.toString());
    } catch {
      // Handle binary protocols here
      return null;
    }
  }
}
