import { v4 as uuidv4 } from 'uuid';
import { GPSMessage, Voyage } from './types.js';

// In-memory storage for GPS messages and voyages
const gpsMessages = new Map<string, GPSMessage>();
const voyages = new Map<string, Voyage>();
const activeVoyages = new Map<string, string>(); // imei -> voyageId
const trackerLastSeen = new Map<string, Date>(); // imei -> last message timestamp

export class GPSHandler {
  
  // Process incoming GPS data from trackers
  processGPSData(rawData: any): GPSMessage | null {
    try {
      // This is a placeholder for GPS message parsing
      // The actual implementation will depend on the GPS tracker protocol
      const message: GPSMessage = {
        id: uuidv4(),
        imei: rawData.imei || 'unknown',
        trackerTimestamp: new Date(rawData.timestamp || Date.now()),
        serverTimestamp: new Date(),
        latitude: parseFloat(rawData.lat || 0),
        longitude: parseFloat(rawData.lng || 0),
        speed: parseFloat(rawData.speed || 0),
        status: this.parseStatus(rawData.status),
        batteryLevel: parseInt(rawData.battery || 100),
      };

      // Handle voyage tracking
      this.handleVoyageTracking(message);

      // Update tracker last seen timestamp
      trackerLastSeen.set(message.imei, message.serverTimestamp);

      // Store the message
      gpsMessages.set(message.id, message);

      return message;
    } catch (error) {
      console.error('Error processing GPS data:', error);
      return null;
    }
  }

  private parseStatus(status: any): 'Inizio' | 'Fine' | 'GPS Fixed' | 'No GPS Fixed' {
    if (typeof status === 'string') {
      switch (status.toLowerCase()) {
        case 'trip_start':
        case 'start':
        case 'inizio':
          return 'Inizio';
        case 'trip_end':
        case 'end':
        case 'fine':
          return 'Fine';
        case 'fixed':
        case 'gps_fixed':
          return 'GPS Fixed';
        case 'no_fixed':
        case 'no_fix':
        case 'no_gps_fixed':
          return 'No GPS Fixed';
        default:
          return 'GPS Fixed';
      }
    }
    return 'GPS Fixed';
  }

  private handleVoyageTracking(message: GPSMessage): void {
    const { imei, status } = message;

    if (status === 'Inizio') {
      // Start a new voyage
      const voyageId = uuidv4();
      const voyage: Voyage = {
        id: voyageId,
        imei,
        startTime: message.trackerTimestamp,
        messages: [message]
      };

      voyages.set(voyageId, voyage);
      activeVoyages.set(imei, voyageId);
      message.voyageId = voyageId;

    } else if (status === 'Fine') {
      // End current voyage
      const voyageId = activeVoyages.get(imei);
      if (voyageId) {
        const voyage = voyages.get(voyageId);
        if (voyage) {
          voyage.endTime = message.trackerTimestamp;
          voyage.messages.push(message);
          message.voyageId = voyageId;
        }
        activeVoyages.delete(imei);
      }

    } else {
      // Add to current voyage if exists
      const voyageId = activeVoyages.get(imei);
      if (voyageId) {
        const voyage = voyages.get(voyageId);
        if (voyage) {
          voyage.messages.push(message);
          message.voyageId = voyageId;
        }
      }
    }
  }

  // Get messages for specific IMEI codes
  getMessagesForImei(imeiCodes: string[]): GPSMessage[] {
    const messages: GPSMessage[] = [];
    
    for (const message of gpsMessages.values()) {
      if (imeiCodes.includes(message.imei)) {
        messages.push(message);
      }
    }
    
    return messages.sort((a, b) => b.serverTimestamp.getTime() - a.serverTimestamp.getTime());
  }

  // Get voyages for specific IMEI codes
  getVoyagesForImei(imeiCodes: string[]): Voyage[] {
    const userVoyages: Voyage[] = [];
    
    for (const voyage of voyages.values()) {
      if (imeiCodes.includes(voyage.imei)) {
        userVoyages.push(voyage);
      }
    }
    
    return userVoyages.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }

  // Get messages for a specific voyage
  getVoyageMessages(voyageId: string): GPSMessage[] {
    const voyage = voyages.get(voyageId);
    return voyage ? voyage.messages : [];
  }

  // Get all messages (for admin purposes)
  getAllMessages(): GPSMessage[] {
    return Array.from(gpsMessages.values())
      .sort((a, b) => b.serverTimestamp.getTime() - a.serverTimestamp.getTime());
  }

  // Get all voyages (for admin purposes)
  getAllVoyages(): Voyage[] {
    return Array.from(voyages.values())
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }

  // Check if a tracker is connected (received data in last 5 minutes)
  isTrackerConnected(imei: string): boolean {
    const lastSeen = trackerLastSeen.get(imei);
    if (!lastSeen) return false;

    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return lastSeen > fiveMinutesAgo;
  }

  // Get tracker connection status for all IMEIs
  getTrackerConnectionStatus(imeiCodes: string[]): { [imei: string]: boolean } {
    const status: { [imei: string]: boolean } = {};
    imeiCodes.forEach(imei => {
      status[imei] = this.isTrackerConnected(imei);
    });
    return status;
  }

  // Get last seen timestamp for a tracker
  getTrackerLastSeen(imei: string): Date | null {
    return trackerLastSeen.get(imei) || null;
  }
}
