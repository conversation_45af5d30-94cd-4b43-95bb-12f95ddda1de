/**
 * GPS Tracker Simulator
 * 
 * This script simulates GPS tracker data for testing the application.
 * It sends realistic GPS coordinates and voyage data to the WebSocket server.
 */

import WebSocket from 'ws';

class GPSSimulator {
  constructor(serverUrl = 'ws://localhost:8090/ws') {
    this.serverUrl = serverUrl;
    this.ws = null;
    this.isConnected = false;
    this.simulationInterval = null;
    
    // Sample IMEI codes for testing
    this.trackers = [
      { imei: '1234567890', name: 'Tracker 1' },
      { imei: '9876543210', name: 'Tracker 2' }
    ];
    
    // Sample route coordinates (New York area)
    this.routes = {
      '1234567890': [
        { lat: 40.7128, lng: -74.0060, speed: 0, status: 'Inizio' },      // NYC
        { lat: 40.7589, lng: -73.9851, speed: 45, status: 'GPS Fixed' },  // Times Square
        { lat: 40.7831, lng: -73.9712, speed: 35, status: 'GPS Fixed' },  // Central Park
        { lat: 40.7505, lng: -73.9934, speed: 25, status: 'GPS Fixed' },  // Hell's Kitchen
        { lat: 40.7282, lng: -74.0776, speed: 55, status: 'GPS Fixed' },  // Jersey City
        { lat: 40.7128, lng: -74.0060, speed: 0, status: 'Fine' }         // Back to start
      ],
      '9876543210': [
        { lat: 40.6892, lng: -74.0445, speed: 0, status: 'Inizio' },      // Brooklyn
        { lat: 40.6782, lng: -73.9442, speed: 40, status: 'GPS Fixed' },  // Brooklyn Heights
        { lat: 40.6892, lng: -73.9442, speed: 30, status: 'GPS Fixed' },  // DUMBO
        { lat: 40.7128, lng: -74.0060, speed: 50, status: 'GPS Fixed' },  // Manhattan
        { lat: 40.6892, lng: -74.0445, speed: 0, status: 'Fine' }         // Back to Brooklyn
      ]
    };
    
    this.currentPositions = {};
    this.voyageStates = {};
  }

  async connect() {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.serverUrl);
        
        this.ws.on('open', () => {
          console.log('Connected to GPS Tracker Management Server');
          this.isConnected = true;
          resolve();
        });
        
        this.ws.on('message', (data) => {
          try {
            const message = JSON.parse(data.toString());
            console.log('Received:', message);
          } catch (error) {
            console.log('Received non-JSON message:', data.toString());
          }
        });
        
        this.ws.on('close', () => {
          console.log('Disconnected from server');
          this.isConnected = false;
        });
        
        this.ws.on('error', (error) => {
          console.error('WebSocket error:', error);
          reject(error);
        });
        
      } catch (error) {
        reject(error);
      }
    });
  }

  sendGPSData(imei, lat, lng, speed, status = 'Fixed', battery = null) {
    if (!this.isConnected) {
      console.log('Not connected to server');
      return;
    }

    const message = {
      type: 'gps_data',
      data: {
        imei: imei,
        timestamp: new Date().toISOString(),
        lat: lat,
        lng: lng,
        speed: speed,
        status: status,
        battery: battery || Math.floor(Math.random() * 30) + 70 // Random battery 70-100%
      }
    };

    this.ws.send(JSON.stringify(message));
    console.log(`Sent GPS data for ${imei}: ${lat}, ${lng}, ${speed} km/h, ${status}`);
  }

  startSimulation() {
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval);
    }

    // Initialize positions
    this.trackers.forEach(tracker => {
      this.currentPositions[tracker.imei] = 0;
      this.voyageStates[tracker.imei] = 'stopped';
    });

    console.log('Starting GPS simulation...');
    
    // Send initial positions
    this.trackers.forEach(tracker => {
      const route = this.routes[tracker.imei];
      const position = route[0];
      this.sendGPSData(tracker.imei, position.lat, position.lng, 0, position.status || 'Inizio');
      this.voyageStates[tracker.imei] = 'moving';
    });

    // Update positions every 5 seconds
    this.simulationInterval = setInterval(() => {
      this.updatePositions();
    }, 5000);
  }

  updatePositions() {
    this.trackers.forEach(tracker => {
      const route = this.routes[tracker.imei];
      const currentIndex = this.currentPositions[tracker.imei];
      
      if (currentIndex < route.length - 1) {
        // Move to next position
        this.currentPositions[tracker.imei]++;
        const position = route[this.currentPositions[tracker.imei]];
        
        let status = position.status || 'GPS Fixed';
        if (this.currentPositions[tracker.imei] === route.length - 1) {
          this.voyageStates[tracker.imei] = 'stopped';
        }

        this.sendGPSData(
          tracker.imei,
          position.lat,
          position.lng,
          position.speed,
          status
        );
      } else {
        // Reset route after a pause
        setTimeout(() => {
          this.currentPositions[tracker.imei] = 0;
          const position = route[0];
          this.sendGPSData(tracker.imei, position.lat, position.lng, 0, position.status || 'Inizio');
          this.voyageStates[tracker.imei] = 'moving';
        }, 10000); // 10 second pause between trips
      }
    });
  }

  stopSimulation() {
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval);
      this.simulationInterval = null;
      console.log('GPS simulation stopped');
    }
  }

  disconnect() {
    this.stopSimulation();
    if (this.ws) {
      this.ws.close();
    }
  }

  // Send some random GPS data for testing
  sendRandomData() {
    const tracker = this.trackers[Math.floor(Math.random() * this.trackers.length)];
    const lat = 40.7128 + (Math.random() - 0.5) * 0.1; // Around NYC
    const lng = -74.0060 + (Math.random() - 0.5) * 0.1;
    const speed = Math.floor(Math.random() * 80);
    const statuses = ['GPS Fixed', 'No GPS Fixed', 'Inizio', 'Fine'];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    this.sendGPSData(tracker.imei, lat, lng, speed, status);
  }
}

// Main execution
async function main() {
  const simulator = new GPSSimulator();
  
  try {
    await simulator.connect();
    
    console.log('\nGPS Tracker Simulator Commands:');
    console.log('1. Type "start" to begin route simulation');
    console.log('2. Type "stop" to stop simulation');
    console.log('3. Type "random" to send random GPS data');
    console.log('4. Type "quit" to exit\n');
    
    // Handle user input
    process.stdin.setEncoding('utf8');
    process.stdin.on('data', (input) => {
      const command = input.trim().toLowerCase();
      
      switch (command) {
        case 'start':
          simulator.startSimulation();
          break;
        case 'stop':
          simulator.stopSimulation();
          break;
        case 'random':
          simulator.sendRandomData();
          break;
        case 'quit':
        case 'exit':
          simulator.disconnect();
          process.exit(0);
          break;
        default:
          console.log('Unknown command. Use: start, stop, random, or quit');
      }
    });
    
  } catch (error) {
    console.error('Failed to connect:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down GPS simulator...');
  process.exit(0);
});

// Run if this is the main module
main();

export default GPSSimulator;
